class ScheduledTask {
  final String id;
  final String usuario;
  final String periodicidad;
  final String mensaje;
  final String bot;
  final String correoElectronico;
  final DateTime createdAt;
  final String? notificationTitle;

  ScheduledTask({
    required this.id,
    required this.usuario,
    required this.periodicidad,
    required this.mensaje,
    required this.bot,
    required this.correoElectronico,
    required this.createdAt,
    this.notificationTitle,
  });

  factory ScheduledTask.fromJson(Map<String, dynamic> json) {
    return ScheduledTask(
      id: json['id'].toString(),
      usuario: json['usuario'] as String,
      periodicidad: json['periodicidad'] as String,
      mensaje: json['mensaje'] as String,
      bot: json['bot'] as String,
      correoElectronico: json['correo_electronico'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      notificationTitle: json['notification_title'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'usuario': usuario,
      'periodicidad': periodicidad,
      'mensaje': mensaje,
      'bot': bot,
      'correo_electronico': correoElectronico,
      'created_at': createdAt.toIso8601String(),
      if (notificationTitle != null) 'notification_title': notificationTitle,
    };
  }
}

enum ScheduleFrequency {
  daily('daily', 'Diario'),
  weekly('weekly', 'Semanal'),
  monthly('monthly', 'Mensual'),
  yearly('yearly', 'Anual'),
  specific('specific', 'Días Específicos');

  const ScheduleFrequency(this.value, this.label);

  final String value;
  final String label;
}

enum CommunicationMethod {
  notification('notification', 'Notificación'),
  whatsapp('whatsapp', 'WhatsApp'),
  email('email', 'Correo Electrónico');

  const CommunicationMethod(this.value, this.label);

  final String value;
  final String label;
}

class ScheduleData {
  final ScheduleFrequency frequency;
  final CommunicationMethod communicationMethod;
  final String? notificationTitle;
  final int selectedDay;
  final int selectedMonth;
  final String selectedWeek;
  final DateTime time;
  final List<String> selectedDays;

  ScheduleData({
    required this.frequency,
    required this.communicationMethod,
    this.notificationTitle,
    this.selectedDay = 1,
    this.selectedMonth = 1,
    this.selectedWeek = '1',
    required this.time,
    this.selectedDays = const [],
  });

  String generateCronExpression() {
    switch (frequency) {
      case ScheduleFrequency.daily:
        return '${time.minute} ${time.hour} * * *';
      case ScheduleFrequency.weekly:
        return '${time.minute} ${time.hour} * * $selectedWeek';
      case ScheduleFrequency.monthly:
        return '${time.minute} ${time.hour} $selectedDay * *';
      case ScheduleFrequency.yearly:
        return '${time.minute} ${time.hour} $selectedDay $selectedMonth *';
      case ScheduleFrequency.specific:
        return '${time.minute} ${time.hour} * * ${selectedDays.join(',')}';
    }
  }
}

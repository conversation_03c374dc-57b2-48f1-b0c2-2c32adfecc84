import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/history_model.dart';

class FavoritesService extends GetxService {
  static const String _favoritesKey =
      'favorite_messages_rid'; // Cambiado para usar RID
  final RxList<int> _favoriteMessages = <int>[].obs;

  Set<int> get favoriteMessages => _favoriteMessages.toSet();

  bool isFavorite(int messageRid) {
    return _favoriteMessages.contains(messageRid);
  }

  /// Fuerza la actualización de la reactividad
  void forceUpdate() {
    _favoriteMessages.refresh();
  }

  @override
  void onInit() {
    super.onInit();
    loadFavorites();
  }

  Future<void> loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = prefs.getStringList(_favoritesKey) ?? [];

      final List<int> favoriteIds = favorites
          .map((idStr) => int.tryParse(idStr))
          .where((id) => id != null)
          .cast<int>()
          .toList();

      _favoriteMessages.clear();
      _favoriteMessages.addAll(favoriteIds);
    } catch (e) {
      if (kDebugMode) {
        print('Error al cargar favoritos: $e');
      }
    }
  }

  Future<bool> toggleFavorite(ChatHistory message, BuildContext context) async {
    try {
      final messageId = message.rid; // Usar RID en lugar de ID
      final isFavorite = _favoriteMessages.contains(messageId);

      if (isFavorite) {
        _favoriteMessages.remove(messageId);
      } else {
        // Evitar duplicados
        if (!_favoriteMessages.contains(messageId)) {
          _favoriteMessages.add(messageId);
        }
      }

      // Forzar actualización de la reactividad
      _favoriteMessages.refresh();

      // Guardar en SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        _favoritesKey,
        _favoriteMessages.map((id) => id.toString()).toList(),
      );

      // Mostrar confirmación
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isFavorite
                  ? 'Mensaje eliminado de favoritos'
                  : 'Mensaje agregado a favoritos',
            ),
            duration: const Duration(seconds: 1),
          ),
        );
      }

      return !isFavorite; // Retorna el nuevo estado
    } catch (e) {
      if (kDebugMode) {
        print('Error al guardar favorito: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al guardar favorito: $e')),
        );
      }
      return _favoriteMessages.contains(message.rid);
    }
  }
}

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/user_models.dart';
import '../constants/api_endpoints.dart';
import '../providers/user_provider.dart';

class AuthService {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();

  AuthService() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
  }

  /// Realiza el login con usuario y contraseña
  Future<LoginResponse> login({
    required String baseUrl,
    required String username,
    required String password,
  }) async {
    try {
      // Preparar los datos del formulario
      final formData = FormData.fromMap({
        'user': username,
        'password': password,
        'grant_type': 'password',
      });

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.loginEndpoint}';

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      // Verificar el código de estado
      if (response.statusCode == 200) {
        // Parsear la respuesta
        final loginResponse = LoginResponse.fromJson(response.data);

        // Guardar datos en el provider
        _userProvider.setLoginData(loginResponse, baseUrl);

        return loginResponse;
      } else {
        throw AuthException(
          'Error en el servidor: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      // Manejar errores específicos de Dio
      throw _handleDioError(e);
    } catch (e) {
      // Manejar otros errores
      throw AuthException('Error inesperado: $e');
    }
  }

  /// Maneja los errores de Dio y los convierte en AuthException
  AuthException _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return AuthException('Tiempo de conexión agotado');
      case DioExceptionType.receiveTimeout:
        return AuthException('Tiempo de respuesta agotado');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Error del servidor';
        return AuthException(
          'Error $statusCode: $message',
          statusCode: statusCode,
        );
      case DioExceptionType.cancel:
        return AuthException('Petición cancelada');
      case DioExceptionType.connectionError:
        return AuthException(
          'Error de conexión. Verifique su conexión a internet',
        );
      case DioExceptionType.unknown:
        return AuthException('Error desconocido: ${e.message}');
      default:
        return AuthException('Error de red: ${e.message}');
    }
  }

  /// Cierra el cliente Dio
  void dispose() {
    _dio.close();
  }
}

/// Excepción personalizada para errores de autenticación
class AuthException implements Exception {
  final String message;
  final int? statusCode;

  AuthException(this.message, {this.statusCode});

  @override
  String toString() => 'AuthException: $message';
}

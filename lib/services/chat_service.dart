import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/bot_model.dart';
import '../providers/user_provider.dart';
import '../providers/history_provider.dart';
import '../constants/api_endpoints.dart';

class ChatService extends GetxService {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();

  ChatService() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(minutes: 2);
  }

  /// Envía un mensaje al bot especificado
  Future<void> sendMessage({required Bot bot, required String message}) async {
    final botId = bot.id.toString();
    final userId = _userProvider.currentUser?.usuario ?? '';

    try {
      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw ChatException('Usuario no autenticado');
      }

      // Agregar el mensaje del usuario inmediatamente al historial local
      _historyProvider.addTemporaryUserMessage(botId, message, userId);

      // Obtener la URL base
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw ChatException('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.queriesEndpoint}';

      // Preparar los datos del formulario
      final formData = FormData.fromMap({
        'user_id': userId,
        'category_name': bot.name, // Usar nombre del bot
        'message': message,
        'provider': 'Google', // Valor por defecto
      });

      // Configurar headers con el token
      final headers = {'Authorization': 'Bearer ${_userProvider.accessToken}'};

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
      );

      // Procesar la respuesta
      if (response.statusCode == 200) {
        // Dar un pequeño delay para que el servidor procese el mensaje
        await Future.delayed(const Duration(milliseconds: 500));

        // Intentar recargar el historial de forma segura
        try {
          await _historyProvider.safeUpdateHistory(botId);
          // Si la recarga es exitosa, remover mensajes temporales ya que el historial está actualizado
          _historyProvider.removeTemporaryMessages(botId);
        } catch (historyError) {
          // Si falla la recarga del historial, solo remover el mensaje temporal
          // pero mantener el resto del historial
          _historyProvider.removeTemporaryMessages(botId);

          if (kDebugMode) {
            debugPrint(
              'Warning: Error al recargar historial después de envío exitoso: $historyError',
            );
          }
          // No lanzar error aquí, el mensaje se envió correctamente al servidor
        }
      } else if (response.statusCode == 401) {
        // Token expirado - remover mensaje temporal
        _historyProvider.removeTemporaryMessages(botId);
        throw ChatException('Token de autorización expirado o inválido');
      } else if (response.statusCode == 424) {
        // Error de API con detalles - remover mensaje temporal
        _historyProvider.removeTemporaryMessages(botId);
        final result = response.data;
        throw ChatException(result['detail'] ?? 'Error en la consulta');
      } else {
        // Otros errores - remover mensaje temporal
        _historyProvider.removeTemporaryMessages(botId);
        throw ChatException('Error al enviar mensaje: ${response.statusCode}');
      }
    } on DioException catch (e) {
      // Remover mensaje temporal en caso de error de red
      _historyProvider.removeTemporaryMessages(botId);
      throw _handleDioError(e);
    } catch (e) {
      // Remover mensaje temporal en caso de cualquier error
      _historyProvider.removeTemporaryMessages(botId);

      // Re-lanzar excepciones de ChatException
      if (e is ChatException) {
        rethrow;
      }
      // Convertir otros errores a ChatException
      throw ChatException('Error inesperado: $e');
    }
  }

  /// Maneja los errores de Dio y los convierte en ChatException
  ChatException _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return ChatException('Tiempo de conexión agotado');
      case DioExceptionType.receiveTimeout:
        return ChatException('Tiempo de respuesta agotado');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['detail'] ?? 'Error del servidor';
        return ChatException('Error $statusCode: $message');
      case DioExceptionType.cancel:
        return ChatException('Petición cancelada');
      case DioExceptionType.connectionError:
        return ChatException(
          'Error de conexión. Verifique su conexión a internet',
        );
      default:
        return ChatException('Error de red: ${e.message}');
    }
  }

  /// Cierra el cliente Dio
  void dispose() {
    _dio.close();
  }
}

/// Excepción personalizada para errores de chat
class ChatException implements Exception {
  final String message;

  ChatException(this.message);

  @override
  String toString() => 'ChatException: $message';
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/bot_model.dart';
import '../providers/history_provider.dart';
import '../controllers/chat_controller.dart';
import '../services/favorites_service.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';
import '../widgets/chat/message_bubble.dart';
import '../widgets/chat/chat_message_input.dart';

class ChatPage extends StatefulWidget {
  final Bot bot;
  final String? messageToResend;

  const ChatPage({super.key, required this.bot, this.messageToResend});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  late final ChatController _controller;

  @override
  void initState() {
    super.initState();
    // El FavoritesService ya debería estar registrado desde main.dart
    // pero verificamos por seguridad
    if (!Get.isRegistered<FavoritesService>()) {
      Get.put(FavoritesService(), permanent: true);
    }

    // Verificar si ya existe el controlador o crear uno nuevo
    final botTag = widget.bot.id.toString();
    if (Get.isRegistered<ChatController>(tag: botTag)) {
      // Si ya existe, obtenerlo
      _controller = Get.find<ChatController>(tag: botTag);
    } else {
      // Si no existe, crear uno nuevo
      _controller = Get.put(ChatController(bot: widget.bot), tag: botTag);
    }

    // Desplazarse al final cuando se carguen los mensajes inicialmente
    // Usar un delay para asegurar que el historial esté cargado
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Si hay un mensaje para reenviar, enviarlo automáticamente
      if (widget.messageToResend != null &&
          widget.messageToResend!.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _resendMessage();
        });
      } else {
        // Solo hacer scroll si no hay mensaje para reenviar
        // (el reenvío ya hará scroll automáticamente)
        Future.delayed(const Duration(milliseconds: 200), () {
          final messages = _historyProvider.currentHistory;
          if (messages.isNotEmpty) {
            _controller.scrollToBottom();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    // Solo eliminar el controlador si esta página lo creó
    // Si ya existía, no lo eliminamos para evitar problemas con otras instancias
    final botTag = widget.bot.id.toString();
    if (Get.isRegistered<ChatController>(tag: botTag)) {
      // Verificar si hay otras páginas usando este controlador
      // En este caso, es mejor no eliminarlo automáticamente
      // El controlador se limpiará cuando sea apropiado
    }
    super.dispose();
  }

  /// Reenvía automáticamente el mensaje desde favoritos
  Future<void> _resendMessage() async {
    if (widget.messageToResend == null || widget.messageToResend!.isEmpty) {
      return;
    }

    try {
      await _controller.sendMessage(message: widget.messageToResend!);

      // Mostrar confirmación de reenvío exitoso
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mensaje reenviado exitosamente'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Mostrar error si falla el reenvío
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al reenviar mensaje: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: theme.colorScheme.secondaryContainer,
              child: Icon(
                widget.bot.icon,
                color: theme.colorScheme.onSecondaryContainer,
              ),
            ),
            const SizedBox(width: 12),
            Text(widget.bot.name),
          ],
        ),
        elevation: 1,
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: Column(
        children: [
          // Área de mensajes
          Expanded(
            child: Obx(() {
              final messages = _historyProvider.currentHistory;

              if (messages.isEmpty) {
                return Center(
                  child: Text(
                    'No hay mensajes. ¡Comienza la conversación!',
                    style: theme.textTheme.bodyLarge,
                  ),
                );
              }

              return ListView.builder(
                controller: _controller.scrollController,
                reverse: false,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                // Detectar gestos de deslizamiento para ocultar el teclado
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                itemBuilder: (context, index) {
                  final message = messages[index];
                  final isUserMessage =
                      message.usuario == message.usuarioDestino;

                  return MessageBubble(
                    message: message,
                    isUserMessage: isUserMessage,
                    botId: widget.bot.id.toString(),
                  );
                },
              );
            }),
          ),

          // Área de entrada de mensaje
          ChatMessageInput(botId: widget.bot.id.toString()),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/scheduled_task_model.dart';
import '../services/scheduler_service.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';

class SchedulerPage extends StatefulWidget {
  final String messageText;
  final String botId;
  final String botName;

  const SchedulerPage({
    super.key,
    required this.messageText,
    required this.botId,
    required this.botName,
  });

  @override
  State<SchedulerPage> createState() => _SchedulerPageState();
}

class _SchedulerPageState extends State<SchedulerPage> {
  final SchedulerService _schedulerService = Get.find<SchedulerService>();

  ScheduleFrequency _frequency = ScheduleFrequency.daily;
  CommunicationMethod _communicationMethod = CommunicationMethod.notification;
  String _notificationTitle = '';
  int _selectedDay = 1;
  int _selectedMonth = 1;
  String _selectedWeek = '1';
  TimeOfDay _time = TimeOfDay.now();
  final List<String> _selectedDays = [];
  bool _isLoading = false;

  final List<String> _weekDays = ['0', '1', '2', '3', '4', '5', '6'];
  final List<String> _weekDayLabels = [
    'Domingo',
    'Lunes',
    'Martes',
    'Miércoles',
    'Jueves',
    'Viernes',
    'Sábado',
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Programar Tarea'),
        backgroundColor: theme.colorScheme.inversePrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCommunicationMethodSection(),
            const SizedBox(height: 16),
            if (_communicationMethod == CommunicationMethod.notification)
              _buildNotificationTitleSection(),
            if (_communicationMethod != CommunicationMethod.notification)
              _buildInfoSection(),
            const SizedBox(height: 16),
            _buildFrequencySection(),
            const SizedBox(height: 16),
            if (_frequency == ScheduleFrequency.monthly ||
                _frequency == ScheduleFrequency.yearly)
              _buildDaySection(),
            if (_frequency == ScheduleFrequency.yearly) _buildMonthSection(),
            if (_frequency == ScheduleFrequency.weekly) _buildWeekDaySection(),
            if (_frequency == ScheduleFrequency.specific)
              _buildSpecificDaysSection(),
            const SizedBox(height: 16),
            _buildTimeSection(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildCommunicationMethodSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Medio de Comunicación',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<CommunicationMethod>(
          value: _communicationMethod,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: CommunicationMethod.values.map((method) {
            return DropdownMenuItem(value: method, child: Text(method.label));
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _communicationMethod = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildNotificationTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Título de la Notificación',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Ingrese el título de la notificación',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          maxLength: 100,
          onChanged: (value) {
            _notificationTitle = value;
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildInfoSection() {
    final method = _communicationMethod == CommunicationMethod.whatsapp
        ? 'WhatsApp'
        : 'Correo Electrónico';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 4,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'ℹ️ La configuración de $method debe realizarse en el ERP. '
              'Contacte al administrador del sistema para habilitar esta funcionalidad.',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Frecuencia', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        DropdownButtonFormField<ScheduleFrequency>(
          value: _frequency,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: ScheduleFrequency.values.map((frequency) {
            return DropdownMenuItem(
              value: frequency,
              child: Text(frequency.label),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _frequency = value;
                _selectedDays
                    .clear(); // Limpiar días específicos al cambiar frecuencia
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildDaySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Día del Mes', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        DropdownButtonFormField<int>(
          value: _selectedDay,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: List.generate(31, (index) {
            final day = index + 1;
            return DropdownMenuItem(value: day, child: Text(day.toString()));
          }),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedDay = value;
              });
            }
          },
        ),
        if (_selectedDay == 31)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Nota: Se ejecutará el último día del mes si el mes no tiene 31 días.',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildMonthSection() {
    final months = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Mes', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        DropdownButtonFormField<int>(
          value: _selectedMonth,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: List.generate(12, (index) {
            final month = index + 1;
            return DropdownMenuItem(value: month, child: Text(months[index]));
          }),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedMonth = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildWeekDaySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Día de la Semana',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedWeek,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _weekDays.asMap().entries.map((entry) {
            return DropdownMenuItem(
              value: entry.value,
              child: Text(_weekDayLabels[entry.key]),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedWeek = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSpecificDaysSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Seleccionar días',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _weekDays.asMap().entries.map((entry) {
            final dayValue = entry.value;
            final dayLabel = _weekDayLabels[entry.key];
            final isSelected = _selectedDays.contains(dayValue);

            return FilterChip(
              label: Text(dayLabel),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedDays.add(dayValue);
                  } else {
                    _selectedDays.remove(dayValue);
                  }
                });
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Hora', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectTime,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Text(
                  _time.format(context),
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSaveSchedule,
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Text('Guardar Programación'),
      ),
    );
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _time,
    );
    if (picked != null && picked != _time) {
      setState(() {
        _time = picked;
      });
    }
  }

  Future<void> _handleSaveSchedule() async {
    // Validaciones
    if (_communicationMethod == CommunicationMethod.notification &&
        _notificationTitle.trim().isEmpty) {
      _showErrorDialog('El título de la notificación es obligatorio');
      return;
    }

    if (_frequency == ScheduleFrequency.specific && _selectedDays.isEmpty) {
      _showErrorDialog('Debe seleccionar al menos un día');
      return;
    }

    // Mostrar mensaje informativo para WhatsApp y Email
    if (_communicationMethod == CommunicationMethod.whatsapp) {
      _showInfoDialog(
        'Configuración Requerida',
        'La configuración de WhatsApp debe realizarse en el ERP. '
            'Por favor, contacte al administrador del sistema.',
      );
      return;
    }

    if (_communicationMethod == CommunicationMethod.email) {
      _showInfoDialog(
        'Configuración Requerida',
        'La configuración de Correo Electrónico debe realizarse en el ERP. '
            'Por favor, contacte al administrador del sistema.',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final scheduleTime = DateTime(
        now.year,
        now.month,
        now.day,
        _time.hour,
        _time.minute,
      );

      final scheduleData = ScheduleData(
        frequency: _frequency,
        communicationMethod: _communicationMethod,
        notificationTitle: _notificationTitle.trim().isNotEmpty
            ? _notificationTitle.trim()
            : null,
        selectedDay: _selectedDay,
        selectedMonth: _selectedMonth,
        selectedWeek: _selectedWeek,
        time: scheduleTime,
        selectedDays: _selectedDays,
      );

      final success = await _schedulerService.createScheduledTask(
        message: widget.messageText,
        botName: widget.botName,
        scheduleData: scheduleData,
      );

      if (success) {
        if (mounted) {
          _showSuccessDialog();
        }
      } else {
        if (mounted) {
          _showErrorDialog('No se pudo programar la tarea');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error al programar la tarea: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Entendido'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Éxito'),
        content: const Text('La tarea ha sido programada correctamente'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.back(); // Regresar a la página anterior
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

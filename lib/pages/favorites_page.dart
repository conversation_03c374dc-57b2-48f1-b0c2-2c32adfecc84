import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/favorites_controller.dart';
import '../models/history_model.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> {
  late final FavoritesController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(FavoritesController());
  }

  @override
  void dispose() {
    Get.delete<FavoritesController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Favoritos'),
        elevation: 1,
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: Column(
        children: [
          // Filtro por bot
          _buildBotFilter(theme),
          // Lista de favoritos
          Expanded(
            child: Obx(() {
              if (_controller.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_controller.error.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: theme.colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error al cargar favoritos',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _controller.error,
                        style: theme.textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _controller.refreshFavorites,
                        child: const Text('Reintentar'),
                      ),
                    ],
                  ),
                );
              }

              final favoriteMessages = _controller.filteredFavoriteMessages;

              if (favoriteMessages.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.favorite_border,
                        size: 64,
                        color: theme.colorScheme.outline,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _controller.selectedBotFilter.isEmpty
                            ? 'No tienes mensajes favoritos'
                            : 'No hay favoritos para este bot',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Los mensajes que marques como favoritos aparecerán aquí',
                        style: theme.textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: _controller.refreshFavorites,
                child: ListView.separated(
                  padding: const EdgeInsets.all(16),
                  itemCount: favoriteMessages.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 12),
                  itemBuilder: (context, index) {
                    final message = favoriteMessages[index];
                    return _buildFavoriteMessageCard(message, theme);
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildBotFilter(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Obx(() {
        final availableBots = _controller.availableBots;

        if (availableBots.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Filtrar por chat:', style: theme.textTheme.labelMedium),
            const SizedBox(height: 8),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Opción "Todos"
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: const Text('Todos'),
                      selected: _controller.selectedBotFilter.isEmpty,
                      onSelected: (selected) {
                        if (selected) {
                          _controller.clearBotFilter();
                        }
                      },
                    ),
                  ),
                  // Opciones de bots
                  ...availableBots.map((bot) {
                    final isSelected =
                        _controller.selectedBotFilter == bot.botId ||
                        _controller.selectedBotFilter == bot.id.toString();

                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(bot.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            _controller.updateBotFilter(bot.botId);
                          } else {
                            _controller.clearBotFilter();
                          }
                        },
                      ),
                    );
                  }),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildFavoriteMessageCard(ChatHistory message, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _controller.navigateToMessageAndResend(message, context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header con bot y fecha
              Row(
                children: [
                  Icon(
                    Icons.smart_toy_outlined,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _controller.getBotNameById(message.usuarioDestino),
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    _formatDate(message.fechaRegistro),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.favorite, color: Colors.red),
                    onPressed: () =>
                        _controller.removeFavorite(message, context),
                    tooltip: 'Quitar de favoritos',
                    constraints: const BoxConstraints(),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Mensaje del usuario
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(
                    alpha: 0.3,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(message.mensaje, style: theme.textTheme.bodyMedium),
              ),
              const SizedBox(height: 8),
              // Indicador de acción
              Row(
                children: [
                  Icon(
                    Icons.touch_app,
                    size: 16,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Toca para reenviar en el chat',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Ayer';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

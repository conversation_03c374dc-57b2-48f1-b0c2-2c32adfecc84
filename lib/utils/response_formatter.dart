import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../models/history_model.dart';

class ResponseFormatter {
  /// Formatea la respuesta según el tipo de mensaje
  static dynamic formatResponse(ChatHistory history) {
    switch (history.mensajeTipo) {
      case 'TablaJSON':
        return _formatTableJson(history.respuesta);
      case 'Texto':
        return history.respuesta.toString();
      default:
        return history.respuesta;
    }
  }

  /// Verifica si un mensaje TablaJSON tiene datos válidos para mostrar
  static bool hasValidTableData(ChatHistory message) {
    if (message.mensajeTipo != 'TablaJSON') {
      return false;
    }

    try {
      final List<Map<String, dynamic>> data = _formatTableJson(
        message.respuesta,
      );
      return data.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error al verificar datos de tabla: $e');
      }
      return false;
    }
  }

  /// Formatea una respuesta de tipo TablaJSON
  static List<Map<String, dynamic>> _formatTableJson(dynamic respuesta) {
    List<Map<String, dynamic>> result = [];

    try {
      // Si la respuesta es una cadena, intentamos parsearla como JSON
      if (respuesta is String) {
        final jsonData = json.decode(respuesta);
        if (jsonData is List) {
          for (var item in jsonData) {
            if (item is Map) {
              result.add(Map<String, dynamic>.from(item));
            }
          }
        } else if (jsonData is Map) {
          // Si es un objeto JSON único, lo convertimos en una lista con un solo elemento
          result.add(Map<String, dynamic>.from(jsonData));
        }
      }
      // Si la respuesta ya es una lista, la procesamos directamente
      else if (respuesta is List) {
        for (var item in respuesta) {
          if (item is Map) {
            result.add(Map<String, dynamic>.from(item));
          }
        }
      }
      // Si la respuesta es un mapa, lo convertimos en una lista con un solo elemento
      else if (respuesta is Map) {
        result.add(Map<String, dynamic>.from(respuesta));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error al parsear JSON en _formatTableJson: $e');
        print('Tipo de respuesta: ${respuesta.runtimeType}');
        print('Contenido de respuesta: $respuesta');
      }
    }

    return result;
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intelibots/pages/login_page.dart';
import 'package:intelibots/pages/bot_selection_page.dart';
import 'package:intelibots/pages/favorites_page.dart';
import 'package:intelibots/pages/chart_page.dart';
import 'package:intelibots/pages/scheduler_page.dart';
import 'package:intelibots/pages/scheduled_tasks_page.dart';
import 'package:intelibots/providers/user_provider.dart';
import 'package:intelibots/providers/bot_provider.dart';
import 'package:intelibots/providers/history_provider.dart';
import 'package:intelibots/services/service_locator.dart';
import 'package:intelibots/models/history_model.dart';

void main() {
  // Inicializar GetX
  WidgetsFlutterBinding.ensureInitialized();

  // Registrar providers
  Get.put(UserProvider());
  Get.put(BotProvider());
  Get.put(HistoryProvider());

  // Inicializar servicios
  ServiceLocator.init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Intelibots',
      theme: ThemeData(
        useMaterial3: true,
        colorSchemeSeed: Colors.deepPurple,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      darkTheme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        colorSchemeSeed: Colors.deepPurple,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      initialRoute: '/login',
      getPages: [
        GetPage(name: '/login', page: () => const LoginPage()),
        GetPage(name: '/bot-selection', page: () => const BotSelectionPage()),
        GetPage(name: '/favorites', page: () => const FavoritesPage()),
        GetPage(
          name: '/chart',
          page: () {
            final message = Get.arguments as ChatHistory;
            return ChartPage(message: message);
          },
        ),
        GetPage(
          name: '/scheduler',
          page: () {
            final args = Get.arguments as Map<String, dynamic>;
            return SchedulerPage(
              messageText: args['messageText'] as String,
              botId: args['botId'] as String,
              botName: args['botName'] as String,
            );
          },
        ),
        GetPage(
          name: '/scheduled-tasks',
          page: () => const ScheduledTasksPage(),
        ),
      ],
    );
  }
}

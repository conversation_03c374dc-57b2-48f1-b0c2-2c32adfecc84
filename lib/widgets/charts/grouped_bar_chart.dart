import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/chart_models.dart';

class GroupedBarChart extends StatelessWidget {
  final ChartData data;
  final double width;
  final double height;
  final bool showValuesOnTopOfBars;
  final double horizontalLabelRotation;

  const GroupedBarChart({
    super.key,
    required this.data,
    required this.width,
    required this.height,
    this.showValuesOnTopOfBars = true,
    this.horizontalLabelRotation = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (data.isEmpty) {
      return SizedBox(
        width: width,
        height: height,
        child: Center(
          child: Text(
            'No hay datos para mostrar',
            style: theme.textTheme.bodyMedium,
          ),
        ),
      );
    }

    // Calcular altura disponible para la gráfica (reservar espacio para leyenda)
    const legendSpacing = 12.0;
    // Calcular altura dinámica de la leyenda basada en el número de elementos
    final legendItemsPerRow = (width / 120).floor().clamp(
      1,
      data.legend.length,
    );
    final legendRows = (data.legend.length / legendItemsPerRow).ceil();
    final legendHeight =
        (legendRows * 24.0) +
        ((legendRows - 1) * 8.0); // 24px por fila + 8px de spacing
    final chartHeight = height - legendSpacing - legendHeight;

    return SizedBox(
      width: width,
      height: height,
      child: Column(
        children: [
          CustomPaint(
            size: Size(width, chartHeight),
            painter: _BarChartPainter(
              data: data,
              theme: theme,
              showValuesOnTopOfBars: showValuesOnTopOfBars,
              horizontalLabelRotation: horizontalLabelRotation,
            ),
          ),
          const SizedBox(height: legendSpacing),
          _buildLegend(theme),
        ],
      ),
    );
  }

  Widget _buildLegend(ThemeData theme) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 16,
      runSpacing: 8,
      children: data.legend.asMap().entries.map((entry) {
        final index = entry.key;
        final label = entry.value;
        final color = _parseColor(data.datasets[index].color);

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 24),
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 4),
            Text(label, style: theme.textTheme.bodySmall),
          ],
        );
      }).toList(),
    );
  }

  Color _parseColor(String colorString) {
    // Remover el # si existe
    String hexColor = colorString.replaceAll('#', '');

    // Agregar alpha si no está presente
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }

    return Color(int.parse(hexColor, radix: 16));
  }
}

class _BarChartPainter extends CustomPainter {
  final ChartData data;
  final ThemeData theme;
  final bool showValuesOnTopOfBars;
  final double horizontalLabelRotation;

  static const double padding = 20;
  static const double labelSpacingFromBars = 12;
  static const double topPadding = 20;
  static const double groupSpacing = 6;
  static const double barSpacing = 3;
  static const double yAxisWidth = 60; // Espacio para el eje Y

  _BarChartPainter({
    required this.data,
    required this.theme,
    required this.showValuesOnTopOfBars,
    required this.horizontalLabelRotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    // Calcular dimensiones basado en etiquetas truncadas
    final maxTruncatedLength = _getMaxTruncatedLabelLength();
    final extraHeightForLongLabels = math.max(
      0,
      (maxTruncatedLength - 10) * 1.5,
    );
    final rotationFactor = horizontalLabelRotation.abs() > 0
        ? 2.5
        : 1; // Más espacio para rotación
    final axisHeight =
        labelSpacingFromBars + (extraHeightForLongLabels * rotationFactor);
    final bottomPadding = padding + axisHeight;
    final chartHeight = size.height - topPadding - bottomPadding;

    // Obtener valor máximo
    final allValues = data.datasets.expand((ds) => ds.data).toList();
    final maxValue = allValues.isEmpty ? 1.0 : allValues.reduce(math.max);
    final adjustedMaxValue = maxValue == 0 ? 1.0 : maxValue;

    // Calcular dimensiones de barras (considerando espacio para eje Y)
    final groupCount = data.labels.length;
    final barsPerGroup = data.datasets.length;
    final numberOfBars = groupCount * barsPerGroup;
    final totalBarSpacing =
        (groupCount - 1) * groupSpacing + numberOfBars * barSpacing;
    final chartAreaWidth = size.width - yAxisWidth - padding;
    final barWidth = (chartAreaWidth - totalBarSpacing) / numberOfBars;

    // Dibujar eje Y con valores de referencia
    _drawYAxis(canvas, size, chartHeight, adjustedMaxValue, bottomPadding);

    // Dibujar barras
    _drawBars(
      canvas,
      size,
      chartHeight,
      adjustedMaxValue,
      barWidth,
      bottomPadding,
    );

    // Dibujar etiquetas del eje X
    _drawXAxisLabels(canvas, size, barWidth, bottomPadding);
  }

  void _drawBars(
    Canvas canvas,
    Size size,
    double chartHeight,
    double maxValue,
    double barWidth,
    double bottomPadding,
  ) {
    for (int groupIndex = 0; groupIndex < data.labels.length; groupIndex++) {
      final groupX =
          yAxisWidth +
          groupIndex *
              (data.datasets.length * (barWidth + barSpacing) + groupSpacing);

      for (int barIndex = 0; barIndex < data.datasets.length; barIndex++) {
        final dataset = data.datasets[barIndex];
        final value = dataset.data[groupIndex];
        final barHeight = maxValue == 0 ? 0 : (value / maxValue) * chartHeight;
        final x = groupX + barIndex * (barWidth + barSpacing);
        final y = topPadding + chartHeight - barHeight;

        // Dibujar barra
        final barRect = Rect.fromLTWH(
          x.toDouble(),
          y.toDouble(),
          barWidth.toDouble(),
          barHeight.toDouble(),
        );
        final barPaint = Paint()
          ..color = _parseColor(dataset.color)
          ..style = PaintingStyle.fill;

        canvas.drawRect(barRect, barPaint);

        // Dibujar valor en la parte superior si está habilitado
        if (showValuesOnTopOfBars && value > 0) {
          _drawValueLabel(canvas, x + barWidth / 2, y - 5, value);
        }
      }
    }
  }

  void _drawXAxisLabels(
    Canvas canvas,
    Size size,
    double barWidth,
    double bottomPadding,
  ) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    for (int groupIndex = 0; groupIndex < data.labels.length; groupIndex++) {
      final label = data.labels[groupIndex];
      final groupX =
          yAxisWidth +
          groupIndex *
              (data.datasets.length * (barWidth + barSpacing) + groupSpacing);
      final groupBlockWidth =
          data.datasets.length * (barWidth + barSpacing) - barSpacing;
      final centerX = groupX + groupBlockWidth / 2;
      final labelY = size.height - (labelSpacingFromBars - 2);

      // Truncar etiquetas largas (más espacio con rotación)
      final displayLabel = _truncateLabel(
        label,
        horizontalLabelRotation.abs() > 0 ? 20 : 12,
      );

      textPainter.text = TextSpan(
        text: displayLabel,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 9,
          color: theme.colorScheme.onSurface,
        ),
      );

      textPainter.layout();

      canvas.save();

      if (horizontalLabelRotation != 0) {
        canvas.translate(centerX, labelY);
        canvas.rotate(horizontalLabelRotation * math.pi / 180);
        textPainter.paint(
          canvas,
          Offset(-textPainter.width / 2, -textPainter.height / 2),
        );
      } else {
        textPainter.paint(
          canvas,
          Offset(centerX - textPainter.width / 2, labelY - textPainter.height),
        );
      }

      canvas.restore();
    }
  }

  void _drawValueLabel(Canvas canvas, double x, double y, double value) {
    final formattedValue = _formatNumber(value);

    final textPainter = TextPainter(
      text: TextSpan(
        text: formattedValue,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 8,
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    // Dibujar fondo para la etiqueta
    final backgroundRect = Rect.fromCenter(
      center: Offset(x, y),
      width: textPainter.width + 8,
      height: textPainter.height + 4,
    );

    final backgroundPaint = Paint()
      ..color = theme.colorScheme.surface.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, const Radius.circular(3)),
      backgroundPaint,
    );

    // Dibujar texto
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y - textPainter.height / 2),
    );
  }

  String _formatNumber(double num) {
    final fixedNum = double.parse(num.toStringAsFixed(2));
    return fixedNum.toStringAsFixed(
      fixedNum.truncateToDouble() == fixedNum ? 0 : 2,
    );
  }

  String _truncateLabel(String label, int maxLength) {
    if (label.length <= maxLength) return label;

    // Intentar cortar en un espacio o guión para mejor legibilidad
    final truncated = label.substring(0, maxLength);
    final lastSpace = truncated.lastIndexOf(' ');
    final lastHyphen = truncated.lastIndexOf('-');
    final lastBreakPoint = math.max(lastSpace, lastHyphen);

    String result;
    // Si hay un punto de corte natural y no está muy al principio, usarlo
    if (lastBreakPoint > maxLength * 0.6) {
      result = label.substring(0, lastBreakPoint);
    } else {
      // Si no, corte directo
      result = truncated;
    }

    // Agregar elipsis visual si se truncó el texto
    return '$result...';
  }

  int _getMaxTruncatedLabelLength() {
    final maxLength = horizontalLabelRotation.abs() > 0 ? 20 : 12;
    return data.labels.fold<int>(0, (max, label) {
      if (label.length <= maxLength) return math.max(max, label.length);

      // Calcular la longitud real del texto truncado (sin los puntos suspensivos)
      final truncated = label.substring(0, maxLength);
      final lastSpace = truncated.lastIndexOf(' ');
      final lastHyphen = truncated.lastIndexOf('-');
      final lastBreakPoint = math.max(lastSpace, lastHyphen);

      int actualLength;
      if (lastBreakPoint > maxLength * 0.6) {
        actualLength = lastBreakPoint;
      } else {
        actualLength = maxLength;
      }

      return math.max(max, actualLength);
    });
  }

  Color _parseColor(String colorString) {
    String hexColor = colorString.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _drawYAxis(
    Canvas canvas,
    Size size,
    double chartHeight,
    double maxValue,
    double bottomPadding,
  ) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Calcular valores de referencia para el eje Y
    final yValues = _calculateYAxisValues(maxValue);

    for (final yValue in yValues) {
      final yPosition =
          topPadding + chartHeight - (yValue / maxValue) * chartHeight;

      // Dibujar línea de referencia horizontal (opcional, sutil)
      final linePaint = Paint()
        ..color = theme.colorScheme.outline.withValues(alpha: 0.2)
        ..strokeWidth = 0.5;

      canvas.drawLine(
        Offset(yAxisWidth, yPosition),
        Offset(size.width, yPosition),
        linePaint,
      );

      // Dibujar valor en el eje Y
      textPainter.text = TextSpan(
        text: _formatYAxisValue(yValue),
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 10,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          yAxisWidth - textPainter.width - 8,
          yPosition - textPainter.height / 2,
        ),
      );
    }
  }

  List<double> _calculateYAxisValues(double maxValue) {
    if (maxValue <= 0) return [0];

    // Calcular un buen número de divisiones (3-5 líneas)
    final magnitude = math
        .pow(10, (math.log(maxValue) / math.ln10).floor())
        .toDouble();
    final normalized = maxValue / magnitude;

    double step;
    if (normalized <= 1) {
      step = magnitude * 0.2;
    } else if (normalized <= 2) {
      step = magnitude * 0.5;
    } else if (normalized <= 5) {
      step = magnitude;
    } else {
      step = magnitude * 2;
    }

    final values = <double>[];
    values.add(0); // Siempre incluir 0

    double current = step;
    while (current <= maxValue) {
      values.add(current);
      current += step;
    }

    // Asegurar que el valor máximo esté incluido o cerca
    if (values.last < maxValue * 0.9) {
      values.add(maxValue);
    }

    return values;
  }

  String _formatYAxisValue(double value) {
    if (value == 0) return '0';

    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(value % 1000000 == 0 ? 0 : 1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(value % 1000 == 0 ? 0 : 1)}K';
    } else if (value >= 1) {
      return value.toStringAsFixed(value % 1 == 0 ? 0 : 1);
    } else {
      return value.toStringAsFixed(2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
